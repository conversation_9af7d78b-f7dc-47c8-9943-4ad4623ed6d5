import je, { useState as b } from "react";
var pe = { exports: {} }, X = {};
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var ke;
function Te() {
  if (ke) return X;
  ke = 1;
  var N = Symbol.for("react.transitional.element"), D = Symbol.for("react.fragment");
  function x(E, f, g) {
    var j = null;
    if (g !== void 0 && (j = "" + g), f.key !== void 0 && (j = "" + f.key), "key" in f) {
      g = {};
      for (var P in f)
        P !== "key" && (g[P] = f[P]);
    } else g = f;
    return f = g.ref, {
      $$typeof: N,
      type: E,
      key: j,
      ref: f !== void 0 ? f : null,
      props: g
    };
  }
  return X.Fragment = D, X.jsx = x, X.jsxs = x, X;
}
var Q = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Ne;
function Ae() {
  return Ne || (Ne = 1, process.env.NODE_ENV !== "production" && function() {
    function N(t) {
      if (t == null) return null;
      if (typeof t == "function")
        return t.$$typeof === me ? null : t.displayName || t.name || null;
      if (typeof t == "string") return t;
      switch (t) {
        case T:
          return "Fragment";
        case te:
          return "Profiler";
        case ee:
          return "StrictMode";
        case G:
          return "Suspense";
        case de:
          return "SuspenseList";
        case O:
          return "Activity";
      }
      if (typeof t == "object")
        switch (typeof t.tag == "number" && console.error(
          "Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."
        ), t.$$typeof) {
          case oe:
            return "Portal";
          case le:
            return (t.displayName || "Context") + ".Provider";
          case fe:
            return (t._context.displayName || "Context") + ".Consumer";
          case ce:
            var d = t.render;
            return t = t.displayName, t || (t = d.displayName || d.name || "", t = t !== "" ? "ForwardRef(" + t + ")" : "ForwardRef"), t;
          case ae:
            return d = t.displayName || null, d !== null ? d : N(t.type) || "Memo";
          case H:
            d = t._payload, t = t._init;
            try {
              return N(t(d));
            } catch {
            }
        }
      return null;
    }
    function D(t) {
      return "" + t;
    }
    function x(t) {
      try {
        D(t);
        var d = !1;
      } catch {
        d = !0;
      }
      if (d) {
        d = console;
        var m = d.error, p = typeof Symbol == "function" && Symbol.toStringTag && t[Symbol.toStringTag] || t.constructor.name || "Object";
        return m.call(
          d,
          "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",
          p
        ), D(t);
      }
    }
    function E(t) {
      if (t === T) return "<>";
      if (typeof t == "object" && t !== null && t.$$typeof === H)
        return "<...>";
      try {
        var d = N(t);
        return d ? "<" + d + ">" : "<...>";
      } catch {
        return "<...>";
      }
    }
    function f() {
      var t = A.A;
      return t === null ? null : t.getOwner();
    }
    function g() {
      return Error("react-stack-top-frame");
    }
    function j(t) {
      if (ne.call(t, "key")) {
        var d = Object.getOwnPropertyDescriptor(t, "key").get;
        if (d && d.isReactWarning) return !1;
      }
      return t.key !== void 0;
    }
    function P(t, d) {
      function m() {
        K || (K = !0, console.error(
          "%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",
          d
        ));
      }
      m.isReactWarning = !0, Object.defineProperty(t, "key", {
        get: m,
        configurable: !0
      });
    }
    function S() {
      var t = N(this.type);
      return ue[t] || (ue[t] = !0, console.error(
        "Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."
      )), t = this.props.ref, t !== void 0 ? t : null;
    }
    function Z(t, d, m, p, k, v, Y, M) {
      return m = v.ref, t = {
        $$typeof: W,
        type: t,
        key: d,
        props: v,
        _owner: k
      }, (m !== void 0 ? m : null) !== null ? Object.defineProperty(t, "ref", {
        enumerable: !1,
        get: S
      }) : Object.defineProperty(t, "ref", { enumerable: !1, value: null }), t._store = {}, Object.defineProperty(t._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: 0
      }), Object.defineProperty(t, "_debugInfo", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: null
      }), Object.defineProperty(t, "_debugStack", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: Y
      }), Object.defineProperty(t, "_debugTask", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: M
      }), Object.freeze && (Object.freeze(t.props), Object.freeze(t)), t;
    }
    function q(t, d, m, p, k, v, Y, M) {
      var h = d.children;
      if (h !== void 0)
        if (p)
          if (re(h)) {
            for (p = 0; p < h.length; p++)
              z(h[p]);
            Object.freeze && Object.freeze(h);
          } else
            console.error(
              "React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead."
            );
        else z(h);
      if (ne.call(d, "key")) {
        h = N(t);
        var C = Object.keys(d).filter(function(se) {
          return se !== "key";
        });
        p = 0 < C.length ? "{key: someKey, " + C.join(": ..., ") + ": ...}" : "{key: someKey}", J[h + p] || (C = 0 < C.length ? "{" + C.join(": ..., ") + ": ...}" : "{}", console.error(
          `A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,
          p,
          h,
          C,
          h
        ), J[h + p] = !0);
      }
      if (h = null, m !== void 0 && (x(m), h = "" + m), j(d) && (x(d.key), h = "" + d.key), "key" in d) {
        m = {};
        for (var V in d)
          V !== "key" && (m[V] = d[V]);
      } else m = d;
      return h && P(
        m,
        typeof t == "function" ? t.displayName || t.name || "Unknown" : t
      ), Z(
        t,
        h,
        v,
        k,
        f(),
        m,
        Y,
        M
      );
    }
    function z(t) {
      typeof t == "object" && t !== null && t.$$typeof === W && t._store && (t._store.validated = 1);
    }
    var $ = je, W = Symbol.for("react.transitional.element"), oe = Symbol.for("react.portal"), T = Symbol.for("react.fragment"), ee = Symbol.for("react.strict_mode"), te = Symbol.for("react.profiler"), fe = Symbol.for("react.consumer"), le = Symbol.for("react.context"), ce = Symbol.for("react.forward_ref"), G = Symbol.for("react.suspense"), de = Symbol.for("react.suspense_list"), ae = Symbol.for("react.memo"), H = Symbol.for("react.lazy"), O = Symbol.for("react.activity"), me = Symbol.for("react.client.reference"), A = $.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, ne = Object.prototype.hasOwnProperty, re = Array.isArray, L = console.createTask ? console.createTask : function() {
      return null;
    };
    $ = {
      "react-stack-bottom-frame": function(t) {
        return t();
      }
    };
    var K, ue = {}, B = $["react-stack-bottom-frame"].bind(
      $,
      g
    )(), y = L(E(g)), J = {};
    Q.Fragment = T, Q.jsx = function(t, d, m, p, k) {
      var v = 1e4 > A.recentlyCreatedOwnerStacks++;
      return q(
        t,
        d,
        m,
        !1,
        p,
        k,
        v ? Error("react-stack-top-frame") : B,
        v ? L(E(t)) : y
      );
    }, Q.jsxs = function(t, d, m, p, k) {
      var v = 1e4 > A.recentlyCreatedOwnerStacks++;
      return q(
        t,
        d,
        m,
        !0,
        p,
        k,
        v ? Error("react-stack-top-frame") : B,
        v ? L(E(t)) : y
      );
    };
  }()), Q;
}
process.env.NODE_ENV === "production" ? pe.exports = Te() : pe.exports = Ae();
var e = pe.exports;
function Pe({
  className: N = "",
  style: D = {}
}) {
  const [x, E] = b(""), [f, g] = b([]), [j, P] = b([]), [S, Z] = b(!1), [q, z] = b(null), [$, W] = b(null), [oe, T] = b([]), [ee, te] = b({
    likedNames: [],
    dislikedNames: [],
    patterns: {
      preferredLength: null,
      preferredStyle: null,
      likedKeywords: [],
      dislikedKeywords: [],
      preferredStructure: null
    }
  }), [fe, le] = b(!1), [ce, G] = b(!1), [de, ae] = b(!1), [H, O] = b(/* @__PURE__ */ new Set()), [me, A] = b(/* @__PURE__ */ new Set()), [ne, re] = b(/* @__PURE__ */ new Set()), [L, K] = b(null), [ue, B] = b(0), [y, J] = b(!1), t = 100, d = (a) => {
    const n = window.scrollY, s = document.documentElement.scrollHeight, o = window.innerHeight, l = s - n - o;
    a(), requestAnimationFrame(() => {
      const i = document.documentElement.scrollHeight, r = window.innerHeight, c = i - l - r;
      Math.abs(i - s) > 5 ? window.scrollTo(0, Math.max(0, c)) : window.scrollTo(0, n);
    });
  }, m = () => {
    const a = document.createElement("canvas"), n = a.getContext("2d");
    n.textBaseline = "top", n.font = "14px Arial", n.fillText("Usage tracking", 2, 2);
    const s = a.toDataURL(), o = navigator.userAgent, l = navigator.language, i = Intl.DateTimeFormat().resolvedOptions().timeZone, r = s + o + l + i;
    let c = 0;
    for (let u = 0; u < r.length; u++) {
      const R = r.charCodeAt(u);
      c = (c << 5) - c + R, c = c & c;
    }
    return `podcast_usage_${Math.abs(c)}`;
  }, p = () => {
    const a = m(), n = (/* @__PURE__ */ new Date()).toDateString(), s = `${a}_${n}`, o = localStorage.getItem(s), l = o ? parseInt(o, 10) : 0;
    return B(l), l >= t ? (J(!0), !1) : !0;
  }, k = (a = 1) => {
    const n = m(), s = (/* @__PURE__ */ new Date()).toDateString(), o = `${n}_${s}`, l = localStorage.getItem(o), r = (l ? parseInt(l, 10) : 0) + a;
    localStorage.setItem(o, r.toString()), B(r), r >= t && J(!0);
  };
  je.useEffect(() => {
    p();
  }, []);
  const v = (a) => {
    let n = a.toLowerCase().replace(/[^a-z0-9\s]/g, "").replace(/\s+/g, " ").replace(/^the\s+/, "").trim();
    const s = ["the", "and", "for", "with", "from", "show", "podcast", "cast", "of", "in", "on", "at", "to", "a", "an"], o = n.split(" "), l = o.filter(
      (r) => r.length > 2 && !s.includes(r)
    );
    if (l.length === 1) {
      const r = l[0];
      return r.length >= 6 && r.length <= 15 ? r : r.length < 6 ? r + "pod" : Y(r);
    }
    if (l.length >= 2) {
      const r = l[0], c = l[1], u = r + c;
      if (u.length >= 6 && u.length <= 15)
        return u;
    }
    const i = o[0];
    return i && i.length >= 3 ? Y(i) + "pod" : "podcast" + Math.random().toString(36).substring(2, 5);
  }, Y = (a) => {
    if (a.length <= 8) return a;
    const n = {
      business: "biz",
      entrepreneur: "entre",
      marketing: "market",
      technology: "tech",
      development: "dev",
      stories: "story",
      lifestyle: "life",
      wellness: "well",
      fitness: "fit",
      health: "heal"
    };
    return n[a] ? n[a] : a.length > 8 ? a.substring(0, 8) : a;
  }, M = async (a) => {
    try {
      const n = await fetch(`https://dns.google/resolve?name=${a}&type=A`, {
        method: "GET",
        headers: {
          Accept: "application/json"
        }
      });
      if (!n.ok)
        return "error";
      const s = await n.json();
      return s.Answer && s.Answer.length > 0 ? "taken" : "available";
    } catch (n) {
      return console.warn("Domain check failed:", n), "error";
    }
  }, h = async (a) => {
    const n = [...a];
    for (let s = 0; s < n.length; s++) {
      const o = v(n[s].name);
      n[s].suggestedDomain = o, n[s].domainStatus = "checking", O((l) => /* @__PURE__ */ new Set([...l, s]));
    }
    g(n);
    for (let s = 0; s < n.length; s++) {
      const o = `${n[s].suggestedDomain}.com`;
      try {
        const l = await M(o);
        g((i) => {
          const r = [...i];
          return r[s] && (r[s].domainStatus = l), r;
        });
      } catch {
        g((i) => {
          const r = [...i];
          return r[s] && (r[s].domainStatus = "error"), r;
        });
      } finally {
        O((l) => {
          const i = new Set(l);
          return i.delete(s), i;
        });
      }
    }
  }, C = (a, n) => {
    if (a.length === 0) return null;
    const s = a.map((r) => r.name.split(" ").length), o = n.map((r) => r.name.split(" ").length), l = s.reduce((r, c) => r + c, 0) / s.length, i = o.length > 0 ? o.reduce((r, c) => r + c, 0) / o.length : 0;
    return l <= 2 && i > 2 ? "short" : l <= 4 && (i <= 2 || i > 4) ? "medium" : l > 4 && i <= 4 ? "long" : l <= 2 ? "short" : l <= 4 ? "medium" : "long";
  }, V = (a, n) => {
    if (a.length === 0) return null;
    const s = a.map((l) => l.description.toLowerCase()).join(" "), o = n.map((l) => l.description.toLowerCase()).join(" ");
    return (s.includes("professional") || s.includes("business")) && !o.includes("professional") && !o.includes("business") ? "professional" : (s.includes("creative") || s.includes("unique")) && !o.includes("creative") && !o.includes("unique") ? "creative" : (s.includes("fun") || s.includes("playful")) && !o.includes("fun") && !o.includes("playful") ? "playful" : "descriptive";
  }, se = (a) => {
    const n = [];
    a.forEach((o) => {
      const l = ["the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"], i = o.name.toLowerCase().split(/\s+/).filter(
        (r) => r.length > 2 && !l.includes(r)
      );
      n.push(...i);
    });
    const s = {};
    return n.forEach((o) => s[o] = (s[o] || 0) + 1), Object.entries(s).sort(([, o], [, l]) => l - o).slice(0, 5).map(([o]) => o);
  }, ge = (a) => {
    const n = a.filter((o) => o.liked === !0), s = a.filter((o) => o.liked === !1);
    return {
      preferredLength: C(n, s),
      preferredStyle: V(n, s),
      likedKeywords: se(n),
      dislikedKeywords: se(s),
      preferredStructure: null
    };
  }, Se = (a, n) => {
    const s = [
      ...n.likedNames.map((r) => r.name.toLowerCase()),
      ...n.dislikedNames.map((r) => r.name.toLowerCase()),
      ...f.map((r) => r.name.toLowerCase())
    ], o = `Create 4 unique, high-converting podcast names for: ${a}`;
    let l = `

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;
    s.length > 0 && (l += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${s.map((r) => `- ${r}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);
    let i = "";
    if (n.patterns.preferredLength && (i += `
Focus on ${{
      short: "1-2 words (punchy and memorable)",
      medium: "2-3 words (balanced and brandable)",
      long: "3-4 words (descriptive but still catchy)"
    }[n.patterns.preferredLength]}. `), n.patterns.preferredStyle && (i += `Use ${{
      descriptive: "clear, straightforward names that explain the content",
      creative: "imaginative, metaphorical, or playful names",
      professional: "authoritative, business-focused names",
      playful: "fun, energetic, engaging names"
    }[n.patterns.preferredStyle] || n.patterns.preferredStyle}. `), n.patterns.likedKeywords.length > 0 && (i += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (i += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), n.likedNames.length > 0) {
      const r = n.likedNames.slice(-2).map((c) => c.name).join('", "');
      i += `
Generate names with similar appeal to: "${r}" (but completely different concepts). `;
    }
    return `${o}${l}${i}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`;
  }, Re = (a, n, s = 1) => {
    const o = [
      ...n.likedNames.map((c) => c.name.toLowerCase()),
      ...n.dislikedNames.map((c) => c.name.toLowerCase()),
      ...f.map((c) => c.name.toLowerCase())
    ], l = `Create ${s} unique, high-converting podcast name${s > 1 ? "s" : ""} for: ${a}`;
    let i = `

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;
    o.length > 0 && (i += `

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${o.map((c) => `- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);
    let r = "";
    return n.patterns.likedKeywords.length > 0 && (r += `
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `), n.patterns.dislikedKeywords.length > 0 && (r += `
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `), `${l}${i}${r}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${s > 1 ? ', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}' : ""}]}`;
  }, he = async (a = !1) => {
    if (!x.trim()) {
      z("Please describe what your podcast is about");
      return;
    }
    if (!p()) {
      z(null);
      return;
    }
    Z(!0), z(null), g([]), a ? G(!0) : (T([]), le(!1), G(!1), ae(!1));
    try {
      const n = a ? Se(x, ee) : `Create 4 unique, high-converting podcast names for: ${x}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`, s = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: n
            }]
          }],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024
          }
        })
      });
      if (!s.ok)
        throw new Error(`API request failed: ${s.status} ${s.statusText}`);
      const o = await s.json();
      if (!o.candidates || !o.candidates[0] || !o.candidates[0].content)
        throw new Error("Invalid response format from API");
      const i = o.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);
      if (!i)
        throw new Error("No valid JSON found in API response");
      const r = JSON.parse(i[0]);
      if (!r.podcast_names || !Array.isArray(r.podcast_names))
        throw new Error("Invalid response structure");
      g(r.podcast_names), k(4), h(r.podcast_names);
      const c = r.podcast_names.map((u, R) => ({
        name: u.name,
        description: u.description,
        liked: null,
        timestamp: Date.now(),
        index: R
      }));
      T(c);
    } catch (n) {
      console.error("Error generating podcast names:", n), z(n instanceof Error ? n.message : "An unexpected error occurred");
    } finally {
      Z(!1), G(!1);
    }
  }, be = async (a, n) => {
    try {
      await navigator.clipboard.writeText(a), W(n), setTimeout(() => W(null), 2e3);
    } catch (s) {
      console.error("Failed to copy text:", s);
    }
  }, xe = async (a, n) => {
    const s = f[a];
    if (s) {
      if (n) {
        const o = window.scrollY, l = document.documentElement.scrollHeight;
        re((i) => /* @__PURE__ */ new Set([...i, a])), d(() => {
          K(`"${s.name}" added to favorites!`);
        }), setTimeout(() => {
          d(() => {
            K(null);
          });
        }, 2e3), setTimeout(() => {
          const i = document.querySelector(".favorites-section"), r = i ? i.scrollHeight : 0;
          P((c) => c.find((u) => u.name === s.name) ? c : [...c, s]), requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              const u = (i ? i.scrollHeight : 0) - r, U = document.documentElement.scrollHeight - l, ie = u > 0 ? u : U, ye = o + ie;
              window.scrollTo(0, Math.max(0, ye));
            });
          });
        }, 100), setTimeout(() => {
          re((i) => {
            const r = new Set(i);
            return r.delete(a), r;
          });
        }, 700), te((i) => {
          const r = { ...i };
          return r.dislikedNames = r.dislikedNames.filter((c) => c.name !== s.name), r.likedNames.find((c) => c.name === s.name) || r.likedNames.push({
            name: s.name,
            description: s.description,
            liked: !0,
            timestamp: Date.now(),
            index: a
          }), r.patterns = ge([...r.likedNames, ...r.dislikedNames]), r;
        }), A((i) => /* @__PURE__ */ new Set([...i, a]));
      } else {
        const o = window.scrollY;
        A((l) => /* @__PURE__ */ new Set([...l, a])), te((l) => {
          const i = { ...l };
          return i.likedNames = i.likedNames.filter((r) => r.name !== s.name), i.dislikedNames.find((r) => r.name === s.name) || i.dislikedNames.push({
            name: s.name,
            description: s.description,
            liked: !1,
            timestamp: Date.now(),
            index: a
          }), i.patterns = ge([...i.likedNames, ...i.dislikedNames]), i;
        }), setTimeout(() => {
          window.scrollTo(0, o);
        }, 0);
      }
      x.trim() && Ee(a), de || ae(!0);
    }
  }, Ee = async (a) => {
    var n, s, o, l, i, r;
    if (p())
      try {
        const c = Re(x, ee, 1), u = await fetch("https://api.yttranscribe.com/podcastNameGenerator", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: c
              }]
            }]
          })
        });
        if (!u.ok)
          throw new Error(`Failed to generate replacement suggestion: ${u.status} ${u.statusText}`);
        const U = (i = (l = (o = (s = (n = (await u.json()).candidates) == null ? void 0 : n[0]) == null ? void 0 : s.content) == null ? void 0 : o.parts) == null ? void 0 : l[0]) == null ? void 0 : i.text;
        if (!U)
          throw new Error("No content in API response");
        const ie = U.match(/\{[\s\S]*\}/);
        if (!ie)
          throw new Error("No valid JSON found in response");
        const F = (r = JSON.parse(ie[0]).podcast_names) == null ? void 0 : r[0];
        if (F) {
          if (g((I) => {
            const w = [...I];
            return w[a] = {
              name: F.name,
              description: F.description,
              suggestedDomain: F.suggestedDomain,
              domainStatus: "checking"
            }, w;
          }), A((I) => {
            const w = new Set(I);
            return w.delete(a), w;
          }), T((I) => I.filter((_) => _.index !== a)), F.suggestedDomain) {
            O((w) => /* @__PURE__ */ new Set([...w, a]));
            const I = await M(F.suggestedDomain);
            g((w) => {
              const _ = [...w];
              return _[a] && (_[a].domainStatus = I), _;
            }), O((w) => {
              const _ = new Set(w);
              return _.delete(a), _;
            });
          }
        } else
          throw new Error("No new name found in API response");
      } catch (c) {
        console.error(`Error generating replacement suggestion for index ${a}:`, c), A((u) => {
          const R = new Set(u);
          return R.delete(a), R;
        }), T((u) => u.filter((U) => U.index !== a));
      }
  }, ve = (a) => {
    a.preventDefault(), he();
  }, we = (a) => {
    a.key === "Enter" && !a.shiftKey && (a.preventDefault(), he());
  };
  return /* @__PURE__ */ e.jsxs(e.Fragment, { children: [
    /* @__PURE__ */ e.jsx("style", { dangerouslySetInnerHTML: { __html: `
        .podcast-name-generator {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
          max-width: 920px;
          margin: 0 auto;
          padding: 20px;
          background: #ffffff;
          border-radius: 16px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          box-sizing: border-box;
        }

        .generator-container {
          width: 100%;
          box-sizing: border-box;
        }

        .header-section {
          text-align: center;
          margin-bottom: 32px;
        }

        .main-title {
          font-size: 3rem;
          font-weight: 800;
          color: #1a1a1a;
          margin: 0 0 16px 0;
          background: linear-gradient(135deg, #6941C7 0%, #8b5cf6 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .main-subtitle {
          font-size: 1.5rem;
          color: #4a5568;
          margin: 0 0 32px 0;
          font-weight: 500;
          line-height: 1.4;
        }

        .benefits-section {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 48px;
          margin: 0 0 48px 0;
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .benefit-checkmark {
          width: 24px;
          height: 24px;
          background-color: #6941C7;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
          flex-shrink: 0;
        }

        .benefit-text {
          color: #2d3748;
          font-size: 1rem;
          font-weight: 500;
          white-space: nowrap;
        }

        .limit-reached-banner {
          margin-bottom: 24px;
          padding: 16px 20px;
          background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
          border: 1px solid #f87171;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(248, 113, 113, 0.1);
          text-align: center;
        }

        .limit-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
        }

        .limit-icon {
          font-size: 2.5rem;
          margin-bottom: 8px;
        }

        .limit-text p {
          margin: 0;
          color: #991b1b;
          font-size: 0.95rem;
          line-height: 1.4;
        }

        .initial-input-section {
          margin-bottom: 32px;
          padding: 26px;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border: 2px solid #e2e8f0;
          border-radius: 20px;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
          text-align: center;
          box-sizing: border-box;
        }

        .input-form {
          margin-bottom: 32px;
        }

        .input-container {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .button-social-container {
          display: flex;
          align-items: center;
          gap: 24px;
          justify-content: space-between;
        }

        .input-field {
          width: 100%;
          padding: 16px 20px;
          font-size: 1rem;
          border: 2px solid #e1e5e9;
          border-radius: 12px;
          resize: vertical;
          min-height: 80px;
          font-family: inherit;
          transition: all 0.2s ease;
          box-sizing: border-box;
        }

        .input-field:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-field:disabled {
          background-color: #f8f9fa;
          cursor: not-allowed;
        }

        .generate-button {
          align-self: flex-start;
          padding: 14px 28px;
          font-size: 1rem;
          font-weight: 600;
          color: white;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          min-width: 160px;
        }

        .generate-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .generate-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        .generate-button.disabled {
          background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
          cursor: not-allowed !important;
          opacity: 0.7;
        }

        .error-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 16px 20px;
          background-color: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 12px;
          color: #dc2626;
          font-weight: 500;
          margin-bottom: 24px;
        }

        .error-icon {
          font-size: 1.2rem;
        }

        .success-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 16px 20px;
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          border: 1px solid #7dd3fc;
          border-radius: 12px;
          color: #0369a1;
          font-weight: 500;
          margin-bottom: 24px;
          animation: slideInFromTop 0.3s ease-out;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .success-icon {
          font-size: 1.2rem;
        }

        @keyframes slideInFromTop {
          0% {
            opacity: 0;
            transform: translateY(-10px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .loading-container {
          text-align: center;
          padding: 40px 20px;
          color: #666;
        }

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .results-container {
          margin-top: 32px;
          padding: 0 26px;
        }

        .favorites-section {
          margin-bottom: 32px;
          padding: 24px;
          background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
          border: 2px solid #10b981;
          border-radius: 16px;
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
          position: relative;
          overflow: hidden;
        }

        .favorites-section::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
          animation: celebrate 3s ease-in-out infinite;
          pointer-events: none;
        }

        @keyframes celebrate {
          0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
          50% { transform: rotate(180deg) scale(1.1); opacity: 0.1; }
        }

        .favorites-header h3 {
          margin: 0 0 12px 0;
          color: #065f46;
          font-size: 1.5rem;
          font-weight: 800;
          text-shadow: 0 1px 2px rgba(16, 185, 129, 0.1);
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .favorites-subtitle {
          margin: 0 0 20px 0;
          color: #047857;
          font-size: 1rem;
          line-height: 1.5;
          font-weight: 500;
          background: rgba(255, 255, 255, 0.7);
          padding: 12px 16px;
          border-radius: 8px;
          border-left: 4px solid #10b981;
        }

        .favorites-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: 20px;
        }

        .favorite-card {
          background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
          border: 2px solid #10b981;
          border-radius: 12px;
          padding: 20px;
          display: flex;
          flex-direction: column;
          box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .favorite-card::before {
          content: '✨';
          position: absolute;
          top: 12px;
          right: 12px;
          font-size: 1.2rem;
          opacity: 0.6;
        }

        .favorite-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
          border-color: #059669;
        }

        .favorite-content {
          flex: 1;
          margin-bottom: 16px;
        }

        .favorite-name {
          margin: 0 0 10px 0;
          color: #1f2937;
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.3;
          word-wrap: break-word;
          hyphens: auto;
        }

        .favorite-description {
          margin: 0 0 12px 0;
          color: #4b5563;
          font-size: 0.95rem;
          line-height: 1.5;
          word-wrap: break-word;
          hyphens: auto;
        }

        .favorite-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
        }

        .input-section-simple {
          margin-bottom: 32px;
        }

        .input-help-message-simple {
          margin-bottom: 16px;
          text-align: center;
        }

        .input-sub-description {
          margin: 0;
          color: #075985;
          font-size: 0.95rem;
          line-height: 1.5;
        }

        .suggestions-section {
          margin-bottom: 24px;
        }

        .suggestions-header h3 {
          margin: 0 0 8px 0;
          color: #1f2937;
          font-size: 1.3rem;
          font-weight: 600;
        }

        .onboarding-banner {
          margin: 20px 0;
          padding: 16px 20px;
          background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
          border: 2px solid #0ea5e9;
          border-radius: 12px;
          animation: slideInDown 0.5s ease-out;
        }

        .onboarding-content {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .onboarding-icon {
          font-size: 1.5rem;
          flex-shrink: 0;
        }

        .onboarding-text {
          color: #0c4a6e;
          font-size: 0.95rem;
          line-height: 1.4;
        }

        @keyframes slideInDown {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .results-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }

        .result-card {
          background: white;
          border: 2px solid #e2e8f0;
          border-radius: 16px;
          padding: 20px;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .result-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .result-card.pending {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-color: #cbd5e0;
        }

        .result-card.pending .result-name {
          color: #64748b;
          font-style: italic;
        }

        .result-card.pending .result-description {
          color: #64748b;
          font-style: italic;
        }

        .result-card.liked {
          border-color: #10b981;
          background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
        }

        .result-card.disliked {
          border-color: #ef4444;
          background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%);
          opacity: 0.8;
          transform: scale(0.98);
        }

        @keyframes flyToFavorites {
          0% {
            transform: scale(1) translateY(0) translateX(0);
            opacity: 1;
            z-index: 1000;
          }
          15% {
            transform: scale(0.95) translateY(-10px) translateX(0);
            opacity: 0.9;
          }
          50% {
            transform: scale(0.8) translateY(-100px) translateX(-20px);
            opacity: 0.7;
          }
          85% {
            transform: scale(0.6) translateY(-200px) translateX(-40px);
            opacity: 0.4;
          }
          100% {
            transform: scale(0.4) translateY(-300px) translateX(-60px);
            opacity: 0;
          }
        }

        .result-card.flying-to-favorites {
          animation: flyToFavorites 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
          pointer-events: none;
          position: relative;
          z-index: 1000;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .result-card.flying-to-favorites::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, #6941c7 0%, #8b5cf6 100%);
          border-radius: 12px;
          opacity: 0.2;
          animation: trailEffect 0.7s ease-out forwards;
          z-index: -1;
        }

        @keyframes trailEffect {
          0% {
            opacity: 0;
            transform: translateY(0);
          }
          20% {
            opacity: 0.3;
            transform: translateY(-20px);
          }
          100% {
            opacity: 0;
            transform: translateY(-100px);
          }
        }

        .result-card.flying-to-favorites::before {
          content: '↗️';
          position: absolute;
          top: -10px;
          right: -10px;
          font-size: 16px;
          animation: directionArrow 0.7s ease-out forwards;
          z-index: 1001;
          pointer-events: none;
        }

        @keyframes directionArrow {
          0% {
            opacity: 0;
            transform: translateY(10px) scale(0.8);
          }
          30% {
            opacity: 0.8;
            transform: translateY(-10px) scale(1);
          }
          70% {
            opacity: 0.6;
            transform: translateY(-50px) scale(0.9);
          }
          100% {
            opacity: 0;
            transform: translateY(-100px) scale(0.7);
          }
        }

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;
          gap: 16px;
        }

        .result-name {
          margin: 0;
          color: #1f2937;
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.3;
          word-wrap: break-word;
          hyphens: auto;
          flex: 1;
        }

        .result-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;
        }

        .feedback-buttons {
          display: flex;
          gap: 4px;
        }

        .feedback-button {
          width: 36px;
          height: 36px;
          border: 2px solid #e2e8f0;
          background: white;
          border-radius: 8px;
          cursor: pointer;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          position: relative;
        }

        .feedback-button:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .feedback-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .feedback-button.loading {
          animation: spin 1s linear infinite;
        }

        .like-button:hover:not(:disabled) {
          border-color: #10b981;
          background: #ecfdf5;
        }

        .like-button.active {
          border-color: #10b981;
          background: #10b981;
          color: white;
          transform: scale(1.1);
        }

        .dislike-button:hover:not(:disabled) {
          border-color: #ef4444;
          background: #fef2f2;
        }

        .dislike-button.active {
          border-color: #ef4444;
          background: #ef4444;
          color: white;
          transform: scale(1.1);
        }

        .copy-button {
          padding: 8px 12px;
          font-size: 0.85rem;
          font-weight: 500;
          color: #4b5563;
          background: #f9fafb;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;
        }

        .copy-button:hover:not(:disabled) {
          background: #f3f4f6;
          border-color: #9ca3af;
          transform: translateY(-1px);
        }

        .copy-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .copy-button.small {
          padding: 6px 10px;
          font-size: 0.8rem;
        }

        .result-description {
          margin: 0 0 16px 0;
          color: #4b5563;
          font-size: 0.95rem;
          line-height: 1.5;
          word-wrap: break-word;
          hyphens: auto;
        }

        .domain-info {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          font-size: 0.85rem;
          margin-top: 12px;
        }

        .domain-info.inline {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 6px;
        }

        .domain-label {
          color: #64748b;
          font-weight: 500;
        }

        .domain-name {
          color: #1e293b;
          font-weight: 600;
        }

        .domain-text {
          background: #e2e8f0;
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.8rem;
          color: #1e293b;
        }

        .domain-status {
          font-weight: 500;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 0.8rem;
        }

        .domain-status.checking {
          color: #0369a1;
          background: #e0f2fe;
        }

        .domain-status.available {
          color: #065f46;
          background: #d1fae5;
        }

        .domain-status.taken {
          color: #991b1b;
          background: #fee2e2;
        }

        .domain-status.error {
          color: #92400e;
          background: #fef3c7;
        }

        /* Social Proof Section */
        .social-proof {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 0;
          margin: 0;
        }

        .user-avatars {
          display: flex;
          margin-right: 16px;
        }

        .avatar {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid rgba(105, 65, 199, 0.3);
          margin-left: -8px;
          overflow: hidden;
          background: white;
        }

        .avatar:first-child {
          margin-left: 0;
        }

        .avatar img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .rating-section {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .stars {
          display: flex;
          gap: 2px;
        }

        .star {
          width: 16px;
          height: 16px;
        }

        .trust-text {
          color: #4a5568;
          font-size: 0.9rem;
          font-weight: 500;
          white-space: nowrap;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .podcast-name-generator {
            padding: 16px;
            margin: 0 16px;
          }

          .main-title {
            font-size: 2.2rem;
          }

          .main-subtitle {
            font-size: 1.2rem;
          }

          .benefits-section {
            flex-direction: column;
            gap: 16px;
            align-items: center;
          }

          .benefit-item {
            justify-content: center;
          }

          .initial-input-section {
            padding: 20px;
          }

          .button-social-container {
            flex-direction: column;
            align-items: stretch;
            gap: 16px;
          }

          .generate-button {
            align-self: stretch;
            text-align: center;
          }

          .results-container {
            padding: 0 16px;
          }

          .results-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .favorites-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .result-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
          }

          .result-actions {
            align-self: stretch;
            justify-content: space-between;
          }

          .domain-info.inline {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }
        }

        @media (max-width: 480px) {
          .main-title {
            font-size: 1.8rem;
          }

          .main-subtitle {
            font-size: 1rem;
          }

          .benefit-text {
            font-size: 0.9rem;
          }

          .social-proof {
            flex-direction: column;
            gap: 12px;
          }

          .user-avatars {
            margin-right: 0;
            margin-bottom: 8px;
          }

          .initial-input-section {
            padding: 16px;
          }

          .favorites-section {
            padding: 16px;
          }

          .result-card {
            padding: 16px;
          }
        }
      ` } }),
    /* @__PURE__ */ e.jsx("div", { className: `podcast-name-generator ${N}`, style: D, children: /* @__PURE__ */ e.jsxs("div", { className: "generator-container", children: [
      /* @__PURE__ */ e.jsxs("div", { className: "header-section", children: [
        /* @__PURE__ */ e.jsx("h1", { className: "main-title", children: "Free Podcast Name Generator" }),
        /* @__PURE__ */ e.jsx("h2", { className: "main-subtitle", children: "Create the Perfect Name for Your Podcast in Seconds" })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: "benefits-section", children: [
        /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
          /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
          /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "100% Free Forever" })
        ] }),
        /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
          /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
          /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "No Sign-up Required" })
        ] }),
        /* @__PURE__ */ e.jsxs("div", { className: "benefit-item", children: [
          /* @__PURE__ */ e.jsx("div", { className: "benefit-checkmark", children: "✓" }),
          /* @__PURE__ */ e.jsx("span", { className: "benefit-text", children: "Instant Results" })
        ] })
      ] }),
      y && /* @__PURE__ */ e.jsx("div", { className: "limit-reached-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "limit-content", children: [
        /* @__PURE__ */ e.jsx("span", { className: "limit-icon", children: "⚠️" }),
        /* @__PURE__ */ e.jsx("div", { className: "limit-text", children: /* @__PURE__ */ e.jsx("p", { children: "You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below." }) })
      ] }) }),
      f.length === 0 && /* @__PURE__ */ e.jsx("div", { className: "initial-input-section", children: /* @__PURE__ */ e.jsx("form", { onSubmit: ve, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
        /* @__PURE__ */ e.jsx(
          "textarea",
          {
            value: x,
            onChange: (a) => E(a.target.value),
            onKeyPress: we,
            placeholder: "Describe what your podcast is about",
            className: "input-field",
            rows: 3,
            disabled: S
          }
        ),
        /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
          /* @__PURE__ */ e.jsx(
            "button",
            {
              type: "submit",
              disabled: S || !x.trim() || y,
              className: `generate-button ${y ? "disabled" : ""}`,
              children: S ? "Generating..." : y ? "Daily Limit Reached" : "Generate Names"
            }
          ),
          /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
              /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
              /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
              /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
              /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
              /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
            ] }),
            /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
              /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
                /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
              ] }),
              /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
            ] })
          ] })
        ] })
      ] }) }) }),
      q && /* @__PURE__ */ e.jsxs("div", { className: "error-message", children: [
        /* @__PURE__ */ e.jsx("span", { className: "error-icon", children: "⚠️" }),
        q
      ] }),
      L && /* @__PURE__ */ e.jsxs("div", { className: "success-message", children: [
        /* @__PURE__ */ e.jsx("span", { className: "success-icon", children: "✨" }),
        L
      ] }),
      S && /* @__PURE__ */ e.jsxs("div", { className: "loading-container", children: [
        /* @__PURE__ */ e.jsx("div", { className: "loading-spinner" }),
        /* @__PURE__ */ e.jsx("p", { children: ce ? "Generating better names based on your preferences..." : "Generating creative podcast names..." })
      ] }),
      f.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "results-container", children: [
        j.length > 0 && /* @__PURE__ */ e.jsxs("div", { className: "favorites-section", children: [
          /* @__PURE__ */ e.jsxs("div", { className: "favorites-header", children: [
            /* @__PURE__ */ e.jsxs("h3", { children: [
              "🏆 Your Winning Podcast Names (",
              j.length,
              ")"
            ] }),
            /* @__PURE__ */ e.jsx("p", { className: "favorites-subtitle", children: "Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!" })
          ] }),
          /* @__PURE__ */ e.jsx("div", { className: "favorites-grid", children: j.map((a, n) => /* @__PURE__ */ e.jsxs("div", { className: "favorite-card", children: [
            /* @__PURE__ */ e.jsxs("div", { className: "favorite-content", children: [
              /* @__PURE__ */ e.jsx("h4", { className: "favorite-name", children: a.name }),
              /* @__PURE__ */ e.jsx("p", { className: "favorite-description", children: a.description }),
              a.suggestedDomain && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
                /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
                /* @__PURE__ */ e.jsx("span", { className: "domain-name", children: a.suggestedDomain }),
                /* @__PURE__ */ e.jsx("span", { className: `domain-status ${a.domainStatus}`, children: a.domainStatus === "available" ? "✅ Available" : a.domainStatus === "taken" ? "❌ Taken" : a.domainStatus === "error" ? "⚠️ Check manually" : "🔍 Checking..." })
              ] })
            ] }),
            /* @__PURE__ */ e.jsx("div", { className: "favorite-actions", children: /* @__PURE__ */ e.jsx(
              "button",
              {
                onClick: () => be(a.name, -1),
                className: "copy-button small",
                title: "Copy to clipboard",
                children: "📋 Copy"
              }
            ) })
          ] }, `fav-${n}`)) })
        ] }),
        /* @__PURE__ */ e.jsxs("div", { className: "input-section-simple", children: [
          /* @__PURE__ */ e.jsx("div", { className: "input-help-message-simple", children: /* @__PURE__ */ e.jsxs("p", { className: "input-sub-description", children: [
            "💡 Want different suggestions? Update your description below - ",
            /* @__PURE__ */ e.jsx("strong", { children: "your favorites will stay safe!" })
          ] }) }),
          /* @__PURE__ */ e.jsx("form", { onSubmit: ve, className: "input-form", children: /* @__PURE__ */ e.jsxs("div", { className: "input-container", children: [
            /* @__PURE__ */ e.jsx(
              "textarea",
              {
                value: x,
                onChange: (a) => E(a.target.value),
                onKeyPress: we,
                placeholder: "Describe what your podcast is about",
                className: "input-field",
                rows: 3,
                disabled: S
              }
            ),
            /* @__PURE__ */ e.jsxs("div", { className: "button-social-container", children: [
              /* @__PURE__ */ e.jsx(
                "button",
                {
                  type: "submit",
                  disabled: S || !x.trim() || y,
                  className: `generate-button ${y ? "disabled" : ""}`,
                  children: S ? "Generating..." : y ? "Daily Limit Reached" : "Generate Names"
                }
              ),
              /* @__PURE__ */ e.jsxs("div", { className: "social-proof", children: [
                /* @__PURE__ */ e.jsxs("div", { className: "user-avatars", children: [
                  /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/32.jpg", alt: "User avatar" }) }),
                  /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/44.jpg", alt: "User avatar" }) }),
                  /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/86.jpg", alt: "User avatar" }) }),
                  /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/women/63.jpg", alt: "User avatar" }) }),
                  /* @__PURE__ */ e.jsx("div", { className: "avatar", children: /* @__PURE__ */ e.jsx("img", { src: "https://randomuser.me/api/portraits/men/54.jpg", alt: "User avatar" }) })
                ] }),
                /* @__PURE__ */ e.jsxs("div", { className: "rating-section", children: [
                  /* @__PURE__ */ e.jsxs("div", { className: "stars", children: [
                    /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                    /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                    /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                    /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) }),
                    /* @__PURE__ */ e.jsx("svg", { className: "star", viewBox: "0 0 24 24", fill: "#fbbf24", children: /* @__PURE__ */ e.jsx("path", { fillRule: "evenodd", d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z", clipRule: "evenodd" }) })
                  ] }),
                  /* @__PURE__ */ e.jsx("span", { className: "trust-text", children: "Trusted by 12k+ users" })
                ] })
              ] })
            ] })
          ] }) })
        ] }),
        /* @__PURE__ */ e.jsxs("div", { className: "suggestions-section", children: [
          /* @__PURE__ */ e.jsx("div", { className: "suggestions-header", children: /* @__PURE__ */ e.jsx("h3", { children: "🎯 Current Suggestions" }) }),
          /* @__PURE__ */ e.jsx("div", { className: "onboarding-banner", children: /* @__PURE__ */ e.jsxs("div", { className: "onboarding-content", children: [
            /* @__PURE__ */ e.jsx("span", { className: "onboarding-icon", children: "💡" }),
            /* @__PURE__ */ e.jsxs("div", { className: "onboarding-text", children: [
              /* @__PURE__ */ e.jsx("strong", { children: "Smart AI Learning:" }),
              " The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."
            ] })
          ] }) }),
          /* @__PURE__ */ e.jsx("div", { className: "results-grid", children: f.map((a, n) => {
            const s = oe.find((c) => c.index === n), o = (s == null ? void 0 : s.liked) === !0, l = (s == null ? void 0 : s.liked) === !1, i = me.has(n), r = H.has(n);
            return /* @__PURE__ */ e.jsxs(
              "div",
              {
                className: `result-card ${o ? "liked" : ""} ${l ? "disliked" : ""} ${i ? "pending" : ""} ${ne.has(n) ? "flying-to-favorites" : ""}`,
                style: {
                  opacity: i ? 0.6 : 1,
                  pointerEvents: i ? "none" : "auto"
                },
                children: [
                  /* @__PURE__ */ e.jsxs("div", { className: "result-header", children: [
                    /* @__PURE__ */ e.jsx("h4", { className: "result-name", children: i ? o ? "Generating new suggestion..." : "Generating better suggestion..." : a.name }),
                    /* @__PURE__ */ e.jsxs("div", { className: "result-actions", children: [
                      /* @__PURE__ */ e.jsxs("div", { className: "feedback-buttons", children: [
                        /* @__PURE__ */ e.jsx(
                          "button",
                          {
                            onClick: () => xe(n, !0),
                            className: `feedback-button like-button ${o ? "active" : ""}`,
                            title: "I like this name",
                            disabled: i,
                            children: "👍"
                          }
                        ),
                        /* @__PURE__ */ e.jsx(
                          "button",
                          {
                            onClick: () => xe(n, !1),
                            className: `feedback-button dislike-button ${l ? "active" : ""} ${i ? "loading" : ""}`,
                            title: i ? "Generating replacement..." : "I don't like this name",
                            disabled: i,
                            children: i ? "🔄" : "👎"
                          }
                        )
                      ] }),
                      /* @__PURE__ */ e.jsx(
                        "button",
                        {
                          onClick: () => be(a.name, n),
                          className: "copy-button",
                          title: "Copy podcast name",
                          disabled: i,
                          children: $ === n ? "✓ Copied!" : "📋 Copy"
                        }
                      )
                    ] })
                  ] }),
                  /* @__PURE__ */ e.jsx("p", { className: "result-description", children: i ? o ? "Added to favorites! Generating a new suggestion..." : "Creating a better suggestion based on your preferences..." : a.description }),
                  a.suggestedDomain && !r && /* @__PURE__ */ e.jsxs("div", { className: "domain-info inline", children: [
                    /* @__PURE__ */ e.jsx("span", { className: "domain-label", children: "Domain:" }),
                    /* @__PURE__ */ e.jsxs("code", { className: "domain-text", children: [
                      a.suggestedDomain,
                      ".com"
                    ] }),
                    /* @__PURE__ */ e.jsxs("span", { className: `domain-status ${a.domainStatus}`, children: [
                      (a.domainStatus === "checking" || H.has(n)) && "⏳ Checking...",
                      a.domainStatus === "available" && "✅ Available",
                      a.domainStatus === "taken" && "❌ Taken",
                      a.domainStatus === "error" && "⚠️ Check manually"
                    ] })
                  ] })
                ]
              },
              n
            );
          }) })
        ] })
      ] })
    ] }) })
  ] });
}
export {
  Pe as default
};

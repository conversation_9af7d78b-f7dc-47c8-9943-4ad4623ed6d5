(function(u,P){typeof exports=="object"&&typeof module<"u"?module.exports=P(require("react")):typeof define=="function"&&define.amd?define(["react"],P):(u=typeof globalThis<"u"?globalThis:u||self,u.PodcastNameGenerator=P(u.React))})(this,function(u){"use strict";var P={exports:{}},H={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fe;function je(){if(fe)return H;fe=1;var N=Symbol.for("react.transitional.element"),O=Symbol.for("react.fragment");function b(E,g,h){var j=null;if(h!==void 0&&(j=""+h),g.key!==void 0&&(j=""+g.key),"key"in g){h={};for(var z in g)z!=="key"&&(h[z]=g[z])}else h=g;return g=h.ref,{$$typeof:N,type:E,key:j,ref:g!==void 0?g:null,props:h}}return H.Fragment=O,H.jsx=b,H.jsxs=b,H}var $={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ge;function Se(){return ge||(ge=1,process.env.NODE_ENV!=="production"&&function(){function N(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ue?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case T:return"Fragment";case ae:return"Profiler";case te:return"StrictMode";case B:return"Suspense";case me:return"SuspenseList";case Y:return"Activity"}if(typeof t=="object")switch(typeof t.tag=="number"&&console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),t.$$typeof){case le:return"Portal";case ce:return(t.displayName||"Context")+".Provider";case he:return(t._context.displayName||"Context")+".Consumer";case de:var d=t.render;return t=t.displayName,t||(t=d.displayName||d.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case ne:return d=t.displayName||null,d!==null?d:N(t.type)||"Memo";case J:d=t._payload,t=t._init;try{return N(t(d))}catch{}}return null}function O(t){return""+t}function b(t){try{O(t);var d=!1}catch{d=!0}if(d){d=console;var m=d.error,f=typeof Symbol=="function"&&Symbol.toStringTag&&t[Symbol.toStringTag]||t.constructor.name||"Object";return m.call(d,"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.",f),O(t)}}function E(t){if(t===T)return"<>";if(typeof t=="object"&&t!==null&&t.$$typeof===J)return"<...>";try{var d=N(t);return d?"<"+d+">":"<...>"}catch{return"<...>"}}function g(){var t=A.A;return t===null?null:t.getOwner()}function h(){return Error("react-stack-top-frame")}function j(t){if(se.call(t,"key")){var d=Object.getOwnPropertyDescriptor(t,"key").get;if(d&&d.isReactWarning)return!1}return t.key!==void 0}function z(t,d){function m(){V||(V=!0,console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)",d))}m.isReactWarning=!0,Object.defineProperty(t,"key",{get:m,configurable:!0})}function S(){var t=N(this.type);return pe[t]||(pe[t]=!0,console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.")),t=this.props.ref,t!==void 0?t:null}function ee(t,d,m,f,k,v,U,F){return m=v.ref,t={$$typeof:q,type:t,key:d,props:v,_owner:k},(m!==void 0?m:null)!==null?Object.defineProperty(t,"ref",{enumerable:!1,get:S}):Object.defineProperty(t,"ref",{enumerable:!1,value:null}),t._store={},Object.defineProperty(t._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:0}),Object.defineProperty(t,"_debugInfo",{configurable:!1,enumerable:!1,writable:!0,value:null}),Object.defineProperty(t,"_debugStack",{configurable:!1,enumerable:!1,writable:!0,value:U}),Object.defineProperty(t,"_debugTask",{configurable:!1,enumerable:!1,writable:!0,value:F}),Object.freeze&&(Object.freeze(t.props),Object.freeze(t)),t}function K(t,d,m,f,k,v,U,F){var x=d.children;if(x!==void 0)if(f)if(re(x)){for(f=0;f<x.length;f++)I(x[f]);Object.freeze&&Object.freeze(x)}else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else I(x);if(se.call(d,"key")){x=N(t);var C=Object.keys(d).filter(function(oe){return oe!=="key"});f=0<C.length?"{key: someKey, "+C.join(": ..., ")+": ...}":"{key: someKey}",Q[x+f]||(C=0<C.length?"{"+C.join(": ..., ")+": ...}":"{}",console.error(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,f,x,C,x),Q[x+f]=!0)}if(x=null,m!==void 0&&(b(m),x=""+m),j(d)&&(b(d.key),x=""+d.key),"key"in d){m={};for(var Z in d)Z!=="key"&&(m[Z]=d[Z])}else m=d;return x&&z(m,typeof t=="function"?t.displayName||t.name||"Unknown":t),ee(t,x,v,k,g(),m,U,F)}function I(t){typeof t=="object"&&t!==null&&t.$$typeof===q&&t._store&&(t._store.validated=1)}var L=u,q=Symbol.for("react.transitional.element"),le=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),te=Symbol.for("react.strict_mode"),ae=Symbol.for("react.profiler"),he=Symbol.for("react.consumer"),ce=Symbol.for("react.context"),de=Symbol.for("react.forward_ref"),B=Symbol.for("react.suspense"),me=Symbol.for("react.suspense_list"),ne=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),Y=Symbol.for("react.activity"),ue=Symbol.for("react.client.reference"),A=L.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,se=Object.prototype.hasOwnProperty,re=Array.isArray,M=console.createTask?console.createTask:function(){return null};L={"react-stack-bottom-frame":function(t){return t()}};var V,pe={},X=L["react-stack-bottom-frame"].bind(L,h)(),y=M(E(h)),Q={};$.Fragment=T,$.jsx=function(t,d,m,f,k){var v=1e4>A.recentlyCreatedOwnerStacks++;return K(t,d,m,!1,f,k,v?Error("react-stack-top-frame"):X,v?M(E(t)):y)},$.jsxs=function(t,d,m,f,k){var v=1e4>A.recentlyCreatedOwnerStacks++;return K(t,d,m,!0,f,k,v?Error("react-stack-top-frame"):X,v?M(E(t)):y)}}()),$}process.env.NODE_ENV==="production"?P.exports=je():P.exports=Se();var e=P.exports;function Re({className:N="",style:O={}}){const[b,E]=u.useState(""),[g,h]=u.useState([]),[j,z]=u.useState([]),[S,ee]=u.useState(!1),[K,I]=u.useState(null),[L,q]=u.useState(null),[le,T]=u.useState([]),[te,ae]=u.useState({likedNames:[],dislikedNames:[],patterns:{preferredLength:null,preferredStyle:null,likedKeywords:[],dislikedKeywords:[],preferredStructure:null}}),[he,ce]=u.useState(!1),[de,B]=u.useState(!1),[me,ne]=u.useState(!1),[J,Y]=u.useState(new Set),[ue,A]=u.useState(new Set),[se,re]=u.useState(new Set),[M,V]=u.useState(null),[pe,X]=u.useState(0),[y,Q]=u.useState(!1),t=100,d=a=>{const n=window.scrollY,r=document.documentElement.scrollHeight,i=window.innerHeight,l=r-n-i;a(),requestAnimationFrame(()=>{const o=document.documentElement.scrollHeight,s=window.innerHeight,c=o-l-s;Math.abs(o-r)>5?window.scrollTo(0,Math.max(0,c)):window.scrollTo(0,n)})},m=()=>{const a=document.createElement("canvas"),n=a.getContext("2d");n.textBaseline="top",n.font="14px Arial",n.fillText("Usage tracking",2,2);const r=a.toDataURL(),i=navigator.userAgent,l=navigator.language,o=Intl.DateTimeFormat().resolvedOptions().timeZone,s=r+i+l+o;let c=0;for(let p=0;p<s.length;p++){const R=s.charCodeAt(p);c=(c<<5)-c+R,c=c&c}return`podcast_usage_${Math.abs(c)}`},f=()=>{const a=m(),n=new Date().toDateString(),r=`${a}_${n}`,i=localStorage.getItem(r),l=i?parseInt(i,10):0;return X(l),l>=t?(Q(!0),!1):!0},k=(a=1)=>{const n=m(),r=new Date().toDateString(),i=`${n}_${r}`,l=localStorage.getItem(i),s=(l?parseInt(l,10):0)+a;localStorage.setItem(i,s.toString()),X(s),s>=t&&Q(!0)};u.useEffect(()=>{f()},[]);const v=a=>{let n=a.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g," ").replace(/^the\s+/,"").trim();const r=["the","and","for","with","from","show","podcast","cast","of","in","on","at","to","a","an"],i=n.split(" "),l=i.filter(s=>s.length>2&&!r.includes(s));if(l.length===1){const s=l[0];return s.length>=6&&s.length<=15?s:s.length<6?s+"pod":U(s)}if(l.length>=2){const s=l[0],c=l[1],p=s+c;if(p.length>=6&&p.length<=15)return p}const o=i[0];return o&&o.length>=3?U(o)+"pod":"podcast"+Math.random().toString(36).substring(2,5)},U=a=>{if(a.length<=8)return a;const n={business:"biz",entrepreneur:"entre",marketing:"market",technology:"tech",development:"dev",stories:"story",lifestyle:"life",wellness:"well",fitness:"fit",health:"heal"};return n[a]?n[a]:a.length>8?a.substring(0,8):a},F=async a=>{try{const n=await fetch(`https://dns.google/resolve?name=${a}&type=A`,{method:"GET",headers:{Accept:"application/json"}});if(!n.ok)return"error";const r=await n.json();return r.Answer&&r.Answer.length>0?"taken":"available"}catch(n){return console.warn("Domain check failed:",n),"error"}},x=async a=>{const n=[...a];for(let r=0;r<n.length;r++){const i=v(n[r].name);n[r].suggestedDomain=i,n[r].domainStatus="checking",Y(l=>new Set([...l,r]))}h(n);for(let r=0;r<n.length;r++){const i=`${n[r].suggestedDomain}.com`;try{const l=await F(i);h(o=>{const s=[...o];return s[r]&&(s[r].domainStatus=l),s})}catch{h(o=>{const s=[...o];return s[r]&&(s[r].domainStatus="error"),s})}finally{Y(l=>{const o=new Set(l);return o.delete(r),o})}}},C=(a,n)=>{if(a.length===0)return null;const r=a.map(s=>s.name.split(" ").length),i=n.map(s=>s.name.split(" ").length),l=r.reduce((s,c)=>s+c,0)/r.length,o=i.length>0?i.reduce((s,c)=>s+c,0)/i.length:0;return l<=2&&o>2?"short":l<=4&&(o<=2||o>4)?"medium":l>4&&o<=4?"long":l<=2?"short":l<=4?"medium":"long"},Z=(a,n)=>{if(a.length===0)return null;const r=a.map(l=>l.description.toLowerCase()).join(" "),i=n.map(l=>l.description.toLowerCase()).join(" ");return(r.includes("professional")||r.includes("business"))&&!i.includes("professional")&&!i.includes("business")?"professional":(r.includes("creative")||r.includes("unique"))&&!i.includes("creative")&&!i.includes("unique")?"creative":(r.includes("fun")||r.includes("playful"))&&!i.includes("fun")&&!i.includes("playful")?"playful":"descriptive"},oe=a=>{const n=[];a.forEach(i=>{const l=["the","a","an","and","or","but","in","on","at","to","for","of","with","by"],o=i.name.toLowerCase().split(/\s+/).filter(s=>s.length>2&&!l.includes(s));n.push(...o)});const r={};return n.forEach(i=>r[i]=(r[i]||0)+1),Object.entries(r).sort(([,i],[,l])=>l-i).slice(0,5).map(([i])=>i)},xe=a=>{const n=a.filter(i=>i.liked===!0),r=a.filter(i=>i.liked===!1);return{preferredLength:C(n,r),preferredStyle:Z(n,r),likedKeywords:oe(n),dislikedKeywords:oe(r),preferredStructure:null}},Ee=(a,n)=>{const r=[...n.likedNames.map(s=>s.name.toLowerCase()),...n.dislikedNames.map(s=>s.name.toLowerCase()),...g.map(s=>s.name.toLowerCase())],i=`Create 4 unique, high-converting podcast names for: ${a}`;let l=`

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates, variations, or similar names
2. Avoid singular/plural variations (e.g., if "Story" exists, don't suggest "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative`;r.length>0&&(l+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${r.map(s=>`- ${s}`).join(`
`)}
Do not create names that are similar to, variations of, or could be confused with any of the above.`);let o="";if(n.patterns.preferredLength&&(o+=`
Focus on ${{short:"1-2 words (punchy and memorable)",medium:"2-3 words (balanced and brandable)",long:"3-4 words (descriptive but still catchy)"}[n.patterns.preferredLength]}. `),n.patterns.preferredStyle&&(o+=`Use ${{descriptive:"clear, straightforward names that explain the content",creative:"imaginative, metaphorical, or playful names",professional:"authoritative, business-focused names",playful:"fun, energetic, engaging names"}[n.patterns.preferredStyle]||n.patterns.preferredStyle}. `),n.patterns.likedKeywords.length>0&&(o+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(o+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),n.likedNames.length>0){const s=n.likedNames.slice(-2).map(c=>c.name).join('", "');o+=`
Generate names with similar appeal to: "${s}" (but completely different concepts). `}return`${i}${l}${o}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`},Te=(a,n,r=1)=>{const i=[...n.likedNames.map(c=>c.name.toLowerCase()),...n.dislikedNames.map(c=>c.name.toLowerCase()),...g.map(c=>c.name.toLowerCase())],l=`Create ${r} unique, high-converting podcast name${r>1?"s":""} for: ${a}`;let o=`

CRITICAL REQUIREMENTS:
1. Must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations of existing names
3. No rearrangements of existing words
4. Should be 2-4 words maximum for memorability
5. Avoid generic words like "Show", "Podcast", "Cast", "Talk"
6. Make brandable, catchy, and easy to pronounce
7. Must clearly relate to the topic but be creative`;i.length>0&&(o+=`

AVOID THESE EXISTING NAMES AND SIMILAR VARIATIONS:
${i.map(c=>`- ${c}`).join(`
`)}
Do not create names similar to any of the above.`);let s="";return n.patterns.likedKeywords.length>0&&(s+=`
Incorporate themes similar to: ${n.patterns.likedKeywords.join(", ")}. `),n.patterns.dislikedKeywords.length>0&&(s+=`
Avoid themes like: ${n.patterns.dislikedKeywords.join(", ")}. `),`${l}${o}${s}

Return as valid JSON: {"podcast_names": [{"name": "Unique Name", "description": "Why this name works", "suggestedDomain": "uniquename.com"}${r>1?', {"name": "Unique Name 2", "description": "Why this works", "suggestedDomain": "uniquename2.com"}':""}]}`},be=async(a=!1)=>{if(!b.trim()){I("Please describe what your podcast is about");return}if(!f()){I(null);return}ee(!0),I(null),h([]),a?B(!0):(T([]),ce(!1),B(!1),ne(!1));try{const n=a?Ee(b,te):`Create 4 unique, high-converting podcast names for: ${b}

CRITICAL REQUIREMENTS:
1. Each name must be completely unique - no duplicates or similar variations
2. Avoid singular/plural variations (e.g., don't suggest both "Story" and "Stories")
3. No names that are just rearrangements of the same words
4. Each name should have a distinct concept and feel
5. Names should be 2-4 words maximum for memorability
6. Avoid generic words like "Show", "Podcast", "Cast", "Talk" in the names
7. Make names brandable, catchy, and easy to pronounce
8. Each name should clearly relate to the topic but be creative

Return as valid JSON: {"podcast_names": [{"name": "Unique Name 1", "description": "Why this name works for the topic"}, {"name": "Unique Name 2", "description": "Why this name works"}, {"name": "Unique Name 3", "description": "Why this name works"}, {"name": "Unique Name 4", "description": "Why this name works"}]}

Remember: Each name must be completely unique and distinct from all others.`,r=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:n}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:1024}})});if(!r.ok)throw new Error(`API request failed: ${r.status} ${r.statusText}`);const i=await r.json();if(!i.candidates||!i.candidates[0]||!i.candidates[0].content)throw new Error("Invalid response format from API");const o=i.candidates[0].content.parts[0].text.match(/\{[\s\S]*\}/);if(!o)throw new Error("No valid JSON found in API response");const s=JSON.parse(o[0]);if(!s.podcast_names||!Array.isArray(s.podcast_names))throw new Error("Invalid response structure");h(s.podcast_names),k(4),x(s.podcast_names);const c=s.podcast_names.map((p,R)=>({name:p.name,description:p.description,liked:null,timestamp:Date.now(),index:R}));T(c)}catch(n){console.error("Error generating podcast names:",n),I(n instanceof Error?n.message:"An unexpected error occurred")}finally{ee(!1),B(!1)}},ve=async(a,n)=>{try{await navigator.clipboard.writeText(a),q(n),setTimeout(()=>q(null),2e3)}catch(r){console.error("Failed to copy text:",r)}},we=async(a,n)=>{const r=g[a];if(r){if(n){const i=window.scrollY,l=document.documentElement.scrollHeight;re(o=>new Set([...o,a])),d(()=>{V(`"${r.name}" added to favorites!`)}),setTimeout(()=>{d(()=>{V(null)})},2e3),setTimeout(()=>{const o=document.querySelector(".favorites-section"),s=o?o.scrollHeight:0;z(c=>c.find(p=>p.name===r.name)?c:[...c,r]),requestAnimationFrame(()=>{requestAnimationFrame(()=>{const p=(o?o.scrollHeight:0)-s,W=document.documentElement.scrollHeight-l,ie=p>0?p:W,Ne=i+ie;window.scrollTo(0,Math.max(0,Ne))})})},100),setTimeout(()=>{re(o=>{const s=new Set(o);return s.delete(a),s})},700),ae(o=>{const s={...o};return s.dislikedNames=s.dislikedNames.filter(c=>c.name!==r.name),s.likedNames.find(c=>c.name===r.name)||s.likedNames.push({name:r.name,description:r.description,liked:!0,timestamp:Date.now(),index:a}),s.patterns=xe([...s.likedNames,...s.dislikedNames]),s}),A(o=>new Set([...o,a]))}else{const i=window.scrollY;A(l=>new Set([...l,a])),ae(l=>{const o={...l};return o.likedNames=o.likedNames.filter(s=>s.name!==r.name),o.dislikedNames.find(s=>s.name===r.name)||o.dislikedNames.push({name:r.name,description:r.description,liked:!1,timestamp:Date.now(),index:a}),o.patterns=xe([...o.likedNames,...o.dislikedNames]),o}),setTimeout(()=>{window.scrollTo(0,i)},0)}b.trim()&&Ae(a),me||ne(!0)}},Ae=async a=>{var n,r,i,l,o,s;if(f())try{const c=Te(b,te,1),p=await fetch("https://api.yttranscribe.com/podcastNameGenerator",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:c}]}]})});if(!p.ok)throw new Error(`Failed to generate replacement suggestion: ${p.status} ${p.statusText}`);const W=(o=(l=(i=(r=(n=(await p.json()).candidates)==null?void 0:n[0])==null?void 0:r.content)==null?void 0:i.parts)==null?void 0:l[0])==null?void 0:o.text;if(!W)throw new Error("No content in API response");const ie=W.match(/\{[\s\S]*\}/);if(!ie)throw new Error("No valid JSON found in response");const G=(s=JSON.parse(ie[0]).podcast_names)==null?void 0:s[0];if(G){if(h(D=>{const w=[...D];return w[a]={name:G.name,description:G.description,suggestedDomain:G.suggestedDomain,domainStatus:"checking"},w}),A(D=>{const w=new Set(D);return w.delete(a),w}),T(D=>D.filter(_=>_.index!==a)),G.suggestedDomain){Y(w=>new Set([...w,a]));const D=await F(G.suggestedDomain);h(w=>{const _=[...w];return _[a]&&(_[a].domainStatus=D),_}),Y(w=>{const _=new Set(w);return _.delete(a),_})}}else throw new Error("No new name found in API response")}catch(c){console.error(`Error generating replacement suggestion for index ${a}:`,c),A(p=>{const R=new Set(p);return R.delete(a),R}),T(p=>p.filter(W=>W.index!==a))}},ye=a=>{a.preventDefault(),be()},ke=a=>{a.key==="Enter"&&!a.shiftKey&&(a.preventDefault(),be())};return e.jsxs(e.Fragment,{children:[e.jsx("style",{dangerouslySetInnerHTML:{__html:`
        .podcast-name-generator {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
          max-width: 920px;
          margin: 0 auto;
          padding: 20px;
          background: #ffffff;
          border-radius: 16px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          box-sizing: border-box;
        }

        .generator-container {
          width: 100%;
          box-sizing: border-box;
        }

        .header-section {
          text-align: center;
          margin-bottom: 32px;
        }

        .main-title {
          font-size: 3rem;
          font-weight: 800;
          color: #1a1a1a;
          margin: 0 0 16px 0;
          background: linear-gradient(135deg, #6941C7 0%, #8b5cf6 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .main-subtitle {
          font-size: 1.5rem;
          color: #4a5568;
          margin: 0 0 32px 0;
          font-weight: 500;
          line-height: 1.4;
        }

        .benefits-section {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 48px;
          margin: 0 0 48px 0;
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .benefit-checkmark {
          width: 24px;
          height: 24px;
          background-color: #6941C7;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
          flex-shrink: 0;
        }

        .benefit-text {
          color: #2d3748;
          font-size: 1rem;
          font-weight: 500;
          white-space: nowrap;
        }

        .limit-reached-banner {
          margin-bottom: 24px;
          padding: 16px 20px;
          background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
          border: 1px solid #f87171;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(248, 113, 113, 0.1);
          text-align: center;
        }

        .limit-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
        }

        .limit-icon {
          font-size: 2.5rem;
          margin-bottom: 8px;
        }

        .limit-text p {
          margin: 0;
          color: #991b1b;
          font-size: 0.95rem;
          line-height: 1.4;
        }

        .initial-input-section {
          margin-bottom: 32px;
          padding: 26px;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          border: 2px solid #e2e8f0;
          border-radius: 20px;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
          text-align: center;
          box-sizing: border-box;
        }

        .input-form {
          margin-bottom: 32px;
        }

        .input-container {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .button-social-container {
          display: flex;
          align-items: center;
          gap: 24px;
          justify-content: space-between;
        }

        .input-field {
          width: 100%;
          padding: 16px 20px;
          font-size: 1rem;
          border: 2px solid #e1e5e9;
          border-radius: 12px;
          resize: vertical;
          min-height: 80px;
          font-family: inherit;
          transition: all 0.2s ease;
          box-sizing: border-box;
        }

        .input-field:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-field:disabled {
          background-color: #f8f9fa;
          cursor: not-allowed;
        }

        .generate-button {
          align-self: flex-start;
          padding: 14px 28px;
          font-size: 1rem;
          font-weight: 600;
          color: white;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          border-radius: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          min-width: 160px;
        }

        .generate-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .generate-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        .generate-button.disabled {
          background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%) !important;
          cursor: not-allowed !important;
          opacity: 0.7;
        }

        .error-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 16px 20px;
          background-color: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 12px;
          color: #dc2626;
          font-weight: 500;
          margin-bottom: 24px;
        }

        .error-icon {
          font-size: 1.2rem;
        }

        .success-message {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 16px 20px;
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          border: 1px solid #7dd3fc;
          border-radius: 12px;
          color: #0369a1;
          font-weight: 500;
          margin-bottom: 24px;
          animation: slideInFromTop 0.3s ease-out;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .success-icon {
          font-size: 1.2rem;
        }

        @keyframes slideInFromTop {
          0% {
            opacity: 0;
            transform: translateY(-10px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .loading-container {
          text-align: center;
          padding: 40px 20px;
          color: #666;
        }

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .results-container {
          margin-top: 32px;
          padding: 0 26px;
        }

        .favorites-section {
          margin-bottom: 32px;
          padding: 24px;
          background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 50%, #a7f3d0 100%);
          border: 2px solid #10b981;
          border-radius: 16px;
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
          position: relative;
          overflow: hidden;
        }

        .favorites-section::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
          animation: celebrate 3s ease-in-out infinite;
          pointer-events: none;
        }

        @keyframes celebrate {
          0%, 100% { transform: rotate(0deg) scale(1); opacity: 0.3; }
          50% { transform: rotate(180deg) scale(1.1); opacity: 0.1; }
        }

        .favorites-header h3 {
          margin: 0 0 12px 0;
          color: #065f46;
          font-size: 1.5rem;
          font-weight: 800;
          text-shadow: 0 1px 2px rgba(16, 185, 129, 0.1);
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .favorites-subtitle {
          margin: 0 0 20px 0;
          color: #047857;
          font-size: 1rem;
          line-height: 1.5;
          font-weight: 500;
          background: rgba(255, 255, 255, 0.7);
          padding: 12px 16px;
          border-radius: 8px;
          border-left: 4px solid #10b981;
        }

        .favorites-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: 20px;
        }

        .favorite-card {
          background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
          border: 2px solid #10b981;
          border-radius: 12px;
          padding: 20px;
          display: flex;
          flex-direction: column;
          box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .favorite-card::before {
          content: '✨';
          position: absolute;
          top: 12px;
          right: 12px;
          font-size: 1.2rem;
          opacity: 0.6;
        }

        .favorite-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
          border-color: #059669;
        }

        .favorite-content {
          flex: 1;
          margin-bottom: 16px;
        }

        .favorite-name {
          margin: 0 0 10px 0;
          color: #1f2937;
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.3;
          word-wrap: break-word;
          hyphens: auto;
        }

        .favorite-description {
          margin: 0 0 12px 0;
          color: #4b5563;
          font-size: 0.95rem;
          line-height: 1.5;
          word-wrap: break-word;
          hyphens: auto;
        }

        .favorite-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
        }

        .input-section-simple {
          margin-bottom: 32px;
        }

        .input-help-message-simple {
          margin-bottom: 16px;
          text-align: center;
        }

        .input-sub-description {
          margin: 0;
          color: #075985;
          font-size: 0.95rem;
          line-height: 1.5;
        }

        .suggestions-section {
          margin-bottom: 24px;
        }

        .suggestions-header h3 {
          margin: 0 0 8px 0;
          color: #1f2937;
          font-size: 1.3rem;
          font-weight: 600;
        }

        .onboarding-banner {
          margin: 20px 0;
          padding: 16px 20px;
          background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
          border: 2px solid #0ea5e9;
          border-radius: 12px;
          animation: slideInDown 0.5s ease-out;
        }

        .onboarding-content {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .onboarding-icon {
          font-size: 1.5rem;
          flex-shrink: 0;
        }

        .onboarding-text {
          color: #0c4a6e;
          font-size: 0.95rem;
          line-height: 1.4;
        }

        @keyframes slideInDown {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .results-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
        }

        .result-card {
          background: white;
          border: 2px solid #e2e8f0;
          border-radius: 16px;
          padding: 20px;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .result-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .result-card.pending {
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-color: #cbd5e0;
        }

        .result-card.pending .result-name {
          color: #64748b;
          font-style: italic;
        }

        .result-card.pending .result-description {
          color: #64748b;
          font-style: italic;
        }

        .result-card.liked {
          border-color: #10b981;
          background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
        }

        .result-card.disliked {
          border-color: #ef4444;
          background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%);
          opacity: 0.8;
          transform: scale(0.98);
        }

        @keyframes flyToFavorites {
          0% {
            transform: scale(1) translateY(0) translateX(0);
            opacity: 1;
            z-index: 1000;
          }
          15% {
            transform: scale(0.95) translateY(-10px) translateX(0);
            opacity: 0.9;
          }
          50% {
            transform: scale(0.8) translateY(-100px) translateX(-20px);
            opacity: 0.7;
          }
          85% {
            transform: scale(0.6) translateY(-200px) translateX(-40px);
            opacity: 0.4;
          }
          100% {
            transform: scale(0.4) translateY(-300px) translateX(-60px);
            opacity: 0;
          }
        }

        .result-card.flying-to-favorites {
          animation: flyToFavorites 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
          pointer-events: none;
          position: relative;
          z-index: 1000;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .result-card.flying-to-favorites::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, #6941c7 0%, #8b5cf6 100%);
          border-radius: 12px;
          opacity: 0.2;
          animation: trailEffect 0.7s ease-out forwards;
          z-index: -1;
        }

        @keyframes trailEffect {
          0% {
            opacity: 0;
            transform: translateY(0);
          }
          20% {
            opacity: 0.3;
            transform: translateY(-20px);
          }
          100% {
            opacity: 0;
            transform: translateY(-100px);
          }
        }

        .result-card.flying-to-favorites::before {
          content: '↗️';
          position: absolute;
          top: -10px;
          right: -10px;
          font-size: 16px;
          animation: directionArrow 0.7s ease-out forwards;
          z-index: 1001;
          pointer-events: none;
        }

        @keyframes directionArrow {
          0% {
            opacity: 0;
            transform: translateY(10px) scale(0.8);
          }
          30% {
            opacity: 0.8;
            transform: translateY(-10px) scale(1);
          }
          70% {
            opacity: 0.6;
            transform: translateY(-50px) scale(0.9);
          }
          100% {
            opacity: 0;
            transform: translateY(-100px) scale(0.7);
          }
        }

        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;
          gap: 16px;
        }

        .result-name {
          margin: 0;
          color: #1f2937;
          font-size: 1.2rem;
          font-weight: 700;
          line-height: 1.3;
          word-wrap: break-word;
          hyphens: auto;
          flex: 1;
        }

        .result-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          flex-shrink: 0;
        }

        .feedback-buttons {
          display: flex;
          gap: 4px;
        }

        .feedback-button {
          width: 36px;
          height: 36px;
          border: 2px solid #e2e8f0;
          background: white;
          border-radius: 8px;
          cursor: pointer;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          position: relative;
        }

        .feedback-button:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .feedback-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .feedback-button.loading {
          animation: spin 1s linear infinite;
        }

        .like-button:hover:not(:disabled) {
          border-color: #10b981;
          background: #ecfdf5;
        }

        .like-button.active {
          border-color: #10b981;
          background: #10b981;
          color: white;
          transform: scale(1.1);
        }

        .dislike-button:hover:not(:disabled) {
          border-color: #ef4444;
          background: #fef2f2;
        }

        .dislike-button.active {
          border-color: #ef4444;
          background: #ef4444;
          color: white;
          transform: scale(1.1);
        }

        .copy-button {
          padding: 8px 12px;
          font-size: 0.85rem;
          font-weight: 500;
          color: #4b5563;
          background: #f9fafb;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;
        }

        .copy-button:hover:not(:disabled) {
          background: #f3f4f6;
          border-color: #9ca3af;
          transform: translateY(-1px);
        }

        .copy-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .copy-button.small {
          padding: 6px 10px;
          font-size: 0.8rem;
        }

        .result-description {
          margin: 0 0 16px 0;
          color: #4b5563;
          font-size: 0.95rem;
          line-height: 1.5;
          word-wrap: break-word;
          hyphens: auto;
        }

        .domain-info {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          font-size: 0.85rem;
          margin-top: 12px;
        }

        .domain-info.inline {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 6px;
        }

        .domain-label {
          color: #64748b;
          font-weight: 500;
        }

        .domain-name {
          color: #1e293b;
          font-weight: 600;
        }

        .domain-text {
          background: #e2e8f0;
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 0.8rem;
          color: #1e293b;
        }

        .domain-status {
          font-weight: 500;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 0.8rem;
        }

        .domain-status.checking {
          color: #0369a1;
          background: #e0f2fe;
        }

        .domain-status.available {
          color: #065f46;
          background: #d1fae5;
        }

        .domain-status.taken {
          color: #991b1b;
          background: #fee2e2;
        }

        .domain-status.error {
          color: #92400e;
          background: #fef3c7;
        }

        /* Social Proof Section */
        .social-proof {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 0;
          margin: 0;
        }

        .user-avatars {
          display: flex;
          margin-right: 16px;
        }

        .avatar {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid rgba(105, 65, 199, 0.3);
          margin-left: -8px;
          overflow: hidden;
          background: white;
        }

        .avatar:first-child {
          margin-left: 0;
        }

        .avatar img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .rating-section {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .stars {
          display: flex;
          gap: 2px;
        }

        .star {
          width: 16px;
          height: 16px;
        }

        .trust-text {
          color: #4a5568;
          font-size: 0.9rem;
          font-weight: 500;
          white-space: nowrap;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .podcast-name-generator {
            padding: 16px;
            margin: 0 16px;
          }

          .main-title {
            font-size: 2.2rem;
          }

          .main-subtitle {
            font-size: 1.2rem;
          }

          .benefits-section {
            flex-direction: column;
            gap: 16px;
            align-items: center;
          }

          .benefit-item {
            justify-content: center;
          }

          .initial-input-section {
            padding: 20px;
          }

          .button-social-container {
            flex-direction: column;
            align-items: stretch;
            gap: 16px;
          }

          .generate-button {
            align-self: stretch;
            text-align: center;
          }

          .results-container {
            padding: 0 16px;
          }

          .results-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .favorites-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .result-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
          }

          .result-actions {
            align-self: stretch;
            justify-content: space-between;
          }

          .domain-info.inline {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }
        }

        @media (max-width: 480px) {
          .main-title {
            font-size: 1.8rem;
          }

          .main-subtitle {
            font-size: 1rem;
          }

          .benefit-text {
            font-size: 0.9rem;
          }

          .social-proof {
            flex-direction: column;
            gap: 12px;
          }

          .user-avatars {
            margin-right: 0;
            margin-bottom: 8px;
          }

          .initial-input-section {
            padding: 16px;
          }

          .favorites-section {
            padding: 16px;
          }

          .result-card {
            padding: 16px;
          }
        }
      `}}),e.jsx("div",{className:`podcast-name-generator ${N}`,style:O,children:e.jsxs("div",{className:"generator-container",children:[e.jsxs("div",{className:"header-section",children:[e.jsx("h1",{className:"main-title",children:"Free Podcast Name Generator"}),e.jsx("h2",{className:"main-subtitle",children:"Create the Perfect Name for Your Podcast in Seconds"})]}),e.jsxs("div",{className:"benefits-section",children:[e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"100% Free Forever"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"No Sign-up Required"})]}),e.jsxs("div",{className:"benefit-item",children:[e.jsx("div",{className:"benefit-checkmark",children:"✓"}),e.jsx("span",{className:"benefit-text",children:"Instant Results"})]})]}),y&&e.jsx("div",{className:"limit-reached-banner",children:e.jsxs("div",{className:"limit-content",children:[e.jsx("span",{className:"limit-icon",children:"⚠️"}),e.jsx("div",{className:"limit-text",children:e.jsx("p",{children:"You've reached our daily usage limit to prevent abuse. Please check back tomorrow or review your favorites below."})})]})}),g.length===0&&e.jsx("div",{className:"initial-input-section",children:e.jsx("form",{onSubmit:ye,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:b,onChange:a=>E(a.target.value),onKeyPress:ke,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:S}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:S||!b.trim()||y,className:`generate-button ${y?"disabled":""}`,children:S?"Generating...":y?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})}),K&&e.jsxs("div",{className:"error-message",children:[e.jsx("span",{className:"error-icon",children:"⚠️"}),K]}),M&&e.jsxs("div",{className:"success-message",children:[e.jsx("span",{className:"success-icon",children:"✨"}),M]}),S&&e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:de?"Generating better names based on your preferences...":"Generating creative podcast names..."})]}),g.length>0&&e.jsxs("div",{className:"results-container",children:[j.length>0&&e.jsxs("div",{className:"favorites-section",children:[e.jsxs("div",{className:"favorites-header",children:[e.jsxs("h3",{children:["🏆 Your Winning Podcast Names (",j.length,")"]}),e.jsx("p",{className:"favorites-subtitle",children:"Congratulations! These are your handpicked favorites. The AI is learning from your excellent taste to create even better suggestions!"})]}),e.jsx("div",{className:"favorites-grid",children:j.map((a,n)=>e.jsxs("div",{className:"favorite-card",children:[e.jsxs("div",{className:"favorite-content",children:[e.jsx("h4",{className:"favorite-name",children:a.name}),e.jsx("p",{className:"favorite-description",children:a.description}),a.suggestedDomain&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsx("span",{className:"domain-name",children:a.suggestedDomain}),e.jsx("span",{className:`domain-status ${a.domainStatus}`,children:a.domainStatus==="available"?"✅ Available":a.domainStatus==="taken"?"❌ Taken":a.domainStatus==="error"?"⚠️ Check manually":"🔍 Checking..."})]})]}),e.jsx("div",{className:"favorite-actions",children:e.jsx("button",{onClick:()=>ve(a.name,-1),className:"copy-button small",title:"Copy to clipboard",children:"📋 Copy"})})]},`fav-${n}`))})]}),e.jsxs("div",{className:"input-section-simple",children:[e.jsx("div",{className:"input-help-message-simple",children:e.jsxs("p",{className:"input-sub-description",children:["💡 Want different suggestions? Update your description below - ",e.jsx("strong",{children:"your favorites will stay safe!"})]})}),e.jsx("form",{onSubmit:ye,className:"input-form",children:e.jsxs("div",{className:"input-container",children:[e.jsx("textarea",{value:b,onChange:a=>E(a.target.value),onKeyPress:ke,placeholder:"Describe what your podcast is about",className:"input-field",rows:3,disabled:S}),e.jsxs("div",{className:"button-social-container",children:[e.jsx("button",{type:"submit",disabled:S||!b.trim()||y,className:`generate-button ${y?"disabled":""}`,children:S?"Generating...":y?"Daily Limit Reached":"Generate Names"}),e.jsxs("div",{className:"social-proof",children:[e.jsxs("div",{className:"user-avatars",children:[e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/32.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/44.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/86.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/women/63.jpg",alt:"User avatar"})}),e.jsx("div",{className:"avatar",children:e.jsx("img",{src:"https://randomuser.me/api/portraits/men/54.jpg",alt:"User avatar"})})]}),e.jsxs("div",{className:"rating-section",children:[e.jsxs("div",{className:"stars",children:[e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})}),e.jsx("svg",{className:"star",viewBox:"0 0 24 24",fill:"#fbbf24",children:e.jsx("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z",clipRule:"evenodd"})})]}),e.jsx("span",{className:"trust-text",children:"Trusted by 12k+ users"})]})]})]})]})})]}),e.jsxs("div",{className:"suggestions-section",children:[e.jsx("div",{className:"suggestions-header",children:e.jsx("h3",{children:"🎯 Current Suggestions"})}),e.jsx("div",{className:"onboarding-banner",children:e.jsxs("div",{className:"onboarding-content",children:[e.jsx("span",{className:"onboarding-icon",children:"💡"}),e.jsxs("div",{className:"onboarding-text",children:[e.jsx("strong",{children:"Smart AI Learning:"})," The AI learns from your preferences! 👍 moves names to favorites, 👎 removes them and generates better suggestions based on your taste."]})]})}),e.jsx("div",{className:"results-grid",children:g.map((a,n)=>{const r=le.find(c=>c.index===n),i=(r==null?void 0:r.liked)===!0,l=(r==null?void 0:r.liked)===!1,o=ue.has(n),s=J.has(n);return e.jsxs("div",{className:`result-card ${i?"liked":""} ${l?"disliked":""} ${o?"pending":""} ${se.has(n)?"flying-to-favorites":""}`,style:{opacity:o?.6:1,pointerEvents:o?"none":"auto"},children:[e.jsxs("div",{className:"result-header",children:[e.jsx("h4",{className:"result-name",children:o?i?"Generating new suggestion...":"Generating better suggestion...":a.name}),e.jsxs("div",{className:"result-actions",children:[e.jsxs("div",{className:"feedback-buttons",children:[e.jsx("button",{onClick:()=>we(n,!0),className:`feedback-button like-button ${i?"active":""}`,title:"I like this name",disabled:o,children:"👍"}),e.jsx("button",{onClick:()=>we(n,!1),className:`feedback-button dislike-button ${l?"active":""} ${o?"loading":""}`,title:o?"Generating replacement...":"I don't like this name",disabled:o,children:o?"🔄":"👎"})]}),e.jsx("button",{onClick:()=>ve(a.name,n),className:"copy-button",title:"Copy podcast name",disabled:o,children:L===n?"✓ Copied!":"📋 Copy"})]})]}),e.jsx("p",{className:"result-description",children:o?i?"Added to favorites! Generating a new suggestion...":"Creating a better suggestion based on your preferences...":a.description}),a.suggestedDomain&&!s&&e.jsxs("div",{className:"domain-info inline",children:[e.jsx("span",{className:"domain-label",children:"Domain:"}),e.jsxs("code",{className:"domain-text",children:[a.suggestedDomain,".com"]}),e.jsxs("span",{className:`domain-status ${a.domainStatus}`,children:[(a.domainStatus==="checking"||J.has(n))&&"⏳ Checking...",a.domainStatus==="available"&&"✅ Available",a.domainStatus==="taken"&&"❌ Taken",a.domainStatus==="error"&&"⚠️ Check manually"]})]})]},n)})})]})]})]})})]})}return Re});
